import { AfterViewInit, ChangeDetectionStrategy, Component, OutputEmitterRef, ViewChild, computed, inject, input, output } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';

import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, startWith } from 'rxjs';

import { ConsentStore } from '@/app/core/stores/consent.store';
import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { LimitedLicenseQualificationProofBoxComponent } from '@/app/features/register-edit/components/organisms/limited-license-qualification-proof-box/limited-license-qualification-proof-box.component';
import { LimitedLicenseQualificationProofBoxFormGroup } from '@/app/features/register-edit/components/organisms/limited-license-qualification-proof-box/limited-license-qualification-proof-box.models';
import { EditFooterPrimaryActionsDirective } from '@/app/features/register-edit/directives/edit-footer-primary-actions.directive';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconDeleteComponent } from '@/app/shared/icons/delete/delete.component';

@Component({
  selector: 'fish-limited-license-qualification-proof-step',
  standalone: true,
  imports: [
    LimitedLicenseQualificationProofBoxComponent,
    EditFooterComponent,
    ButtonComponent,
    IconDeleteComponent,
    EditFooterPrimaryActionsDirective,
    TranslateModule,
  ],
  templateUrl: './limited-license-qualification-proof-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slideInOut', [
      transition(':enter', [
        style({
          width: '0px',
          opacity: 0,
          marginLeft: '0px'
        }),
        animate('240ms ease-out', style({
          width: '*',
          opacity: 1,
          marginLeft: '12px'
        }))
      ]),
      transition(':leave', [
        animate('240ms ease-out', style({
          width: '0px',
          opacity: 0,
          marginLeft: '0px'
        }))
      ])
    ])
  ]
})
export class LimitedLicenseQualificationProofStepComponent implements EditFormStep<LimitedLicenseQualificationProofBoxFormGroup>, AfterViewInit {
  public readonly showDeclineButton = input<boolean>(false);

  // OUTPUTS
  public continueButtonClicked: OutputEmitterRef<void> = output();

  public backButtonClicked: OutputEmitterRef<void> = output();

  // FIELDS
  public readonly canContinue$ = new BehaviorSubject<boolean>(false);

  public formGroup!: LimitedLicenseQualificationProofBoxFormGroup;

  @ViewChild(LimitedLicenseQualificationProofBoxComponent)
  public formComponent!: LimitedLicenseQualificationProofBoxComponent;

  protected readonly isDeclineButtonVisible = computed(() => {
    return this.showDeclineButton() && !this.consentStore.getLimitedLicenseConsentInfo().disablityCertificateVerified;
  });

  // DEPENDENCIES
  private readonly consentStore = inject(ConsentStore);

  public ngAfterViewInit(): void {
    this.formGroup = this.formComponent.formGroup;

    this.formGroup.statusChanges.pipe(startWith(this.formGroup.valid)).subscribe(() => {
      this.canContinue$.next(this.formGroup.valid);
    });
  }

  protected handleContinue(): void {
    this.formComponent.validate();
    if (this.formGroup.valid) {
      this.continueButtonClicked.emit();
    }
  }

  protected handleDecline(): void {
    console.debug('The backend endpoint has not been implemented yet.');
  }
}
